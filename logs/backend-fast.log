/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework_simplejwt/__init__.py:1: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  from pkg_resources import DistributionNotFound, get_distribution
Performing system checks...

System check identified some issues:

WARNINGS:
?: (guardian.W001) Guardian authentication backend is not hooked. You can add this in settings as eg: `AUTHENTICATION_BACKENDS = ('django.contrib.auth.backends.ModelBackend', 'guardian.backends.ObjectPermissionBackend')`.
?: (staticfiles.W004) The directory '/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/static' in the STATICFILES_DIRS setting does not exist.
?: (urls.W005) URL namespace 'authentication' isn't unique. You may not be able to reverse all URLs in this namespace
?: (urls.W005) URL namespace 'clients' isn't unique. You may not be able to reverse all URLs in this namespace
?: (urls.W005) URL namespace 'projects' isn't unique. You may not be able to reverse all URLs in this namespace
?: (urls.W005) URL namespace 'tasks' isn't unique. You may not be able to reverse all URLs in this namespace
?: (urls.W005) URL namespace 'team' isn't unique. You may not be able to reverse all URLs in this namespace

System check identified 7 issues (0 silenced).
June 04, 2025 - 17:41:54
Django version 4.2.9, using settings 'mtbrmg_erp.settings'
Starting development server at http://127.0.0.1:8000/
Quit the server with CONTROL-C.

