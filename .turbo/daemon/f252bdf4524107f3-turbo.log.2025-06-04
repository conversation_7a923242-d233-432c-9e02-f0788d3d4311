2025-06-04T00:04:01.777872Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-presence@1.1.2_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-controllable-state@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-collapsible@1.1.2_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/react-remove-scroll@2.7.1_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/react-remove-scroll-bar@2.3.8_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-roving-focus@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-focus-scope@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-size@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@floating-ui+react-dom@2.1.2_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-id@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-direction@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/lucide-react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-dialog@1.1.4_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-context@1.1.1_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/webpack-runtime.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/login/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/layout.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-progress@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/cache-life.d.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-dismissable-layer@1.1.3_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/react-style-singleton@2.2.3_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-portal@1.1.3_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-arrow@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/login/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-layout-effect@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-collection@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-callback-ref@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-focus-guards@1.1.1_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/next@15.2.4_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/use-callback-ref@1.3.3_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-escape-keydown@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-menu@2.1.4_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-popper@1.2.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/use-sidecar@1.1.3_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/package.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-dropdown-menu@2.1.4_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>")}
2025-06-04T00:04:01.778913Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T00:04:01.779615Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T00:04:01.875043Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo/daemon/f252bdf4524107f3-turbo.log.2025-06-04"), AnchoredSystemPathBuf(".turbo/daemon/f252bdf4524107f3-turbo.log.2025-06-03")}
2025-06-04T00:04:01.875053Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-04T00:04:01.974828Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/login/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/webpack.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.db71b920621d6c90.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/db71b920621d6c90.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js")}
2025-06-04T00:04:01.974837Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T00:04:01.982033Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T00:04:25.273955Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-select@2.1.4_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/package.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-visually-hidden@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/clients/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-previous@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/clients/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/login/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/cache-life.d.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/clients"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/login/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/clients"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/lucide-react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/layout.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/webpack-runtime.js")}
2025-06-04T00:04:25.273989Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T00:04:25.274350Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T00:04:25.474008Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.2f4ebca846993dda.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/2f4ebca846993dda.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/login/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/clients"), AnchoredSystemPathBuf("apps/frontend/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/webpack.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js")}
2025-06-04T00:04:25.474021Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T00:04:25.499330Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T00:04:25.581993Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/clients/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/clients/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/webpack.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/2f4ebca846993dda.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/login/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.2f4ebca846993dda.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js")}
2025-06-04T00:04:25.582004Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T00:04:25.582238Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T00:04:26.774182Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/projects/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/lucide-react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/clients/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/webpack-runtime.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/package.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/login/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/clients/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/layout.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/projects"), AnchoredSystemPathBuf("apps/frontend/.next/types/cache-life.d.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/page.js")}
2025-06-04T00:04:26.774204Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T00:04:26.774528Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T00:04:26.876075Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/clients/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/projects/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.ed064bb1c8f0e5cf.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/login/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/projects"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/webpack.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/ed064bb1c8f0e5cf.webpack.hot-update.json")}
2025-06-04T00:04:26.876089Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T00:04:26.891406Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T00:04:28.673305Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/projects/new/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/webpack-runtime.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/package.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/layout.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/new"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/new/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/lucide-react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/clients/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/projects/new"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/projects/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/login/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/cache-life.d.ts")}
2025-06-04T00:04:28.673317Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T00:04:28.673428Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T00:04:28.774163Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/e96bd100076e181b.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/projects/new/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/webpack.js"), AnchoredSystemPathBuf("apps/frontend/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/login/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/new/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/clients/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.e96bd100076e181b.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/projects/new"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/page_client-reference-manifest.js")}
2025-06-04T00:04:28.774173Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T00:04:28.774238Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T00:04:30.273666Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/tasks/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/package.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/clients/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/clients/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/tasks/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/new/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/tasks"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/layout.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/projects/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/projects/new/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/lucide-react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/tasks"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/cache-life.d.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/login/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/webpack-runtime.js")}
2025-06-04T00:04:30.273687Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T00:04:30.274014Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T00:04:30.373765Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/tasks/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/webpack.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/1c2711ca3b27846a.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.1c2711ca3b27846a.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/tasks"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/clients/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/new/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/login/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/tasks/page.js")}
2025-06-04T00:04:30.373775Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T00:04:30.373910Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T00:05:30.491877Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/4.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/2.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/3.pack.gz_")}
2025-06-04T00:05:30.492269Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T00:05:30.530788Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T00:05:30.674173Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/3.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/7.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/4.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/6.pack.gz_")}
2025-06-04T00:05:30.674190Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T00:05:30.676826Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T00:05:30.773938Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/3.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/2.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/3.pack.gz_")}
2025-06-04T00:05:30.773948Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T00:05:30.773991Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T00:05:30.974293Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/2.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/3.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/4.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/3.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/6.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/6.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/4.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/2.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz.old")}
2025-06-04T00:05:30.974301Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T00:05:30.974345Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T00:05:31.173861Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/7.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/4.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/3.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/3.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/7.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/4.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz.old"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz")}
2025-06-04T00:05:31.173869Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T00:05:31.173923Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T13:45:28.552558Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo/cookies/.turbo-cookie")}
2025-06-04T13:45:28.552567Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-04T13:45:28.653146Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo/cookies/1.cookie")}
2025-06-04T13:45:28.653154Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-04T13:45:34.352684Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/shared/dist/index.d.ts"), AnchoredSystemPathBuf("packages/shared/dist/types/projects.d.ts"), AnchoredSystemPathBuf("packages/shared/dist/constants/index.d.ts"), AnchoredSystemPathBuf("packages/shared/dist/types/auth.js"), AnchoredSystemPathBuf("packages/shared/dist/types/clients.js"), AnchoredSystemPathBuf("packages/shared/dist/types/auth.d.ts"), AnchoredSystemPathBuf("packages/shared/dist/types/tasks.d.ts"), AnchoredSystemPathBuf("packages/shared/dist/index.js"), AnchoredSystemPathBuf("packages/shared/dist/constants/index.js"), AnchoredSystemPathBuf("packages/shared/dist/types/projects.js"), AnchoredSystemPathBuf("packages/shared/dist/types/index.js"), AnchoredSystemPathBuf("packages/shared/dist/types/tasks.js"), AnchoredSystemPathBuf("packages/shared/dist/types/index.d.ts"), AnchoredSystemPathBuf("packages/shared/dist/utils.d.ts"), AnchoredSystemPathBuf("packages/shared/dist/utils.js"), AnchoredSystemPathBuf("packages/shared/dist/types/clients.d.ts")}
2025-06-04T13:45:34.353140Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/shared"), path: AnchoredSystemPathBuf("packages/shared") }}))
2025-06-04T13:45:36.453049Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/backend/logs/django.log")}
2025-06-04T13:45:36.453070Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/backend"), path: AnchoredSystemPathBuf("apps/backend") }}))
2025-06-04T13:45:38.952471Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@floating-ui+react-dom@2.1.2_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/3abc2a74bccacda0-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/5b5924403aa3f821-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/7f153a52d75489f4.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-focus-scope@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/9003d31b0bf0a63e-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-context@1.1.1_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/next-themes@0.4.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/react-remove-scroll-bar@2.3.8_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/555550940b3f3995-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-portal@1.1.3_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-arrow@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-dropdown-menu@2.1.4_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-size@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/sonner@1.7.4_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/d03708b9b9cff7ea-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/trace"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/029616a09f89e9be-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/978ecdeb712d33d6.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-progress@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/19b3491949ee2ef5.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-visually-hidden@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@tanstack+react-query-devtools@5.79.0_@tanstack+react-query@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/24803c66df11f210-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/package.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-escape-keydown@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/1e49f3a0b5c4a1ea.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/9efac92d680e57b6-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/078954fc9e8513ac-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-slot@1.1.1_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/46b6fa8c8b58af7b.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-callback-ref@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/b45a9d9d1da72c1b-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/main-app.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/23e96e077d8dc23f.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/264d9441dabd7a59.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-popper@1.2.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/628cf4a163773be3-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/use-callback-ref@1.3.3_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/webpack.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-dialog@1.1.4_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/zustand@4.5.7_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/633457081244afec._.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/lucide-react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/c3d28d0a4af5320e-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/use-sidecar@1.1.3_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-previous@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-focus-guards@1.1.1_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-collapsible@1.1.2_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-id@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/18a0a2d19c91cbed.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/d065d29785e639fa.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/e8907a8c65b82518.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/443b65745b6df830-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-dismissable-layer@1.1.3_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/b3db2c632ef95c16.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@hookform+resolvers@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/18d6d756b83deaa1-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/package.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-compose-refs@1.1.1_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-layout-effect@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/_app-pages-browser_node_modules_pnpm_tanstack_query-devtools_5_76_0_node_modules_tanstack_que-3f626e.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-controllable-state@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/cc4bdf7bf08e5351-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-roving-focus@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/8cb74166f4c238e7-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/interception-route-rewrite-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/cache-life.d.ts"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/16d2dcee9871f6d8.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/1a9bc3eed9a415ed.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/3612c969537d1f89-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-select@2.1.4_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/b53732993cf3ac57-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/next@15.2.4_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/use-sync-external-store@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@tanstack+react-query@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/react-hook-form@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/6bfeb4e83b5c8e0c.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/d3c939daec3cf0fb-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/react-style-singleton@2.2.3_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-direction@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-label@2.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app-pages-internals.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-collection@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-menu@2.1.4_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-presence@1.1.2_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-primitive@2.0.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/react-remove-scroll@2.7.1_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/_app-pages-browser_node_modules_pnpm_tanstack_query-devtools_5_76_0_node_modules_tanstack_que-fef58b.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/webpack-runtime.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/0612184e5fd566f7-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/23acdaac58de6ef3-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/54685c0c990b5328-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/3ad6e6f895797568-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/683edc0915df848c-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/b626faedfa289fd1-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/dbdc77a4a9d637b5-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/polyfills.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/e59a6a9b6eba13d5-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/ceced752341815e9-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>")}
2025-06-04T13:45:38.952497Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T13:45:39.052643Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/finance/budget"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/finance/expenses"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/finance/cash-flow/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/team/designers/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/finance/budget/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/login"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/team/designers/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/login/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/team/sales"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/finance/expenses"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/finance/cash-flow"), AnchoredSystemPathBuf("apps/frontend/.next/static/css/app/layout.css"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.d065d29785e639fa.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/server"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/team/media-buyers"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.1e49f3a0b5c4a1ea.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/tasks"), AnchoredSystemPathBuf("apps/frontend/.next/server/app"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/tasks"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/team/developers/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/projects"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/finance/budget/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/_not-found"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/finance/cash-flow/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/team/designers"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.fa45d0a1c0e5c095.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/team/designers"), AnchoredSystemPathBuf("apps/frontend/.next/static"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/team/media-buyers"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.23e96e077d8dc23f.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/finance/revenue"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/team/designers/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/projects"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/team/sales/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/finance/revenue"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/finance"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/finance/budget/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/finance/reports"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/projects/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/team"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/team/media-buyers"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/finance/expenses"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.264d9441dabd7a59.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/_not-found/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.6bfeb4e83b5c8e0c.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/clients"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/team/developers/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/tasks/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/finance/reports/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/login/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/login/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/team/media-buyers/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/_not-found/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/css"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.46b6fa8c8b58af7b.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/_not-found/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/team/designers/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/types"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/finance/revenue"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/clients"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/finance/reports"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/clients"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.1a9bc3eed9a415ed.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.b3db2c632ef95c16.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/finance/reports/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/team/designers"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/finance"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/tasks"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/finance/reports/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/layout.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/clients/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/finance/cash-flow/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/login/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/layout.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/clients/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/fa45d0a1c0e5c095.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/development"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/tasks/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.978ecdeb712d33d6.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/finance"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/team/sales"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/finance/reports/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/tasks/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/css/app"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/finance/budget"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/tasks/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/finance/expenses/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/team/sales/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/team/developers"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/finance/cash-flow"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/team/media-buyers/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/package.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/team"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/finance/expenses/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.18a0a2d19c91cbed.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.19b3491949ee2ef5.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/_not-found"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/login"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/projects/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/finance/reports"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/team"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/finance/revenue/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/team/media-buyers/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/login"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/clients/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/finance/cash-flow"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.16d2dcee9871f6d8.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/team/sales/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/finance/expenses/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/clients/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/finance/expenses/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/finance/revenue/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/finance/budget"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/team/developers/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/finance/revenue/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/team/media-buyers/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/app"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/finance/cash-flow/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.7f153a52d75489f4.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/finance/revenue/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/team/developers"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/team/developers/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/finance/budget/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/team/developers"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/app/layout.19b3491949ee2ef5.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/team/sales"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.e8907a8c65b82518.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/team/sales/page.js")}
2025-06-04T13:45:39.052666Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T13:45:39.052736Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T13:45:39.353114Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks"), AnchoredSystemPathBuf("apps/frontend/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/trace"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/interception-route-rewrite-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/package.json"), AnchoredSystemPathBuf("apps/frontend/.next/server"), AnchoredSystemPathBuf("apps/frontend/.next/static/development"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/cache-life.d.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/polyfills.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-manifest.json")}
2025-06-04T13:45:39.353123Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T13:45:39.353164Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T13:45:39.552678Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/build-manifest.json")}
2025-06-04T13:45:39.552688Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T13:45:39.552734Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T13:45:39.652686Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/types/cache-life.d.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/interception-route-rewrite-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/package.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-manifest.json")}
2025-06-04T13:45:39.652698Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T13:45:39.652771Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T13:45:50.652780Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-focus-guards@1.1.1_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-callback-ref@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-layout-effect@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-previous@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/finance/reports/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/finance/reports/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-select@2.1.4_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-roving-focus@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-size@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@tanstack+react-query@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-collapsible@1.1.2_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/next@15.2.4_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/app"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-id@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/sonner@1.7.4_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/react-style-singleton@2.2.3_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/finance/reports"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@tanstack+react-query-devtools@5.79.0_@tanstack+react-query@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/cache-life.d.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/use-sync-external-store@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/package.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-dropdown-menu@2.1.4_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-context@1.1.1_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-dialog@1.1.4_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/finance"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/finance"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-presence@1.1.2_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-arrow@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-direction@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-popper@1.2.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-escape-keydown@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@floating-ui+react-dom@2.1.2_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/lucide-react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/next-themes@0.4.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/zustand@4.5.7_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-collection@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-slot@1.1.1_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-compose-refs@1.1.1_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/use-sidecar@1.1.3_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/webpack-runtime.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/layout.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-primitive@2.0.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/use-callback-ref@1.3.3_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-visually-hidden@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/react-remove-scroll@2.7.1_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-focus-scope@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/finance/reports"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-portal@1.1.3_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-dismissable-layer@1.1.3_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-controllable-state@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/react-remove-scroll-bar@2.3.8_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-menu@2.1.4_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>")}
2025-06-04T13:45:50.652807Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T13:45:50.772164Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T13:45:51.852850Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/0.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/17.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/15.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/7.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/16.pack.gz_")}
2025-06-04T13:45:51.852881Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T13:45:51.853003Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T13:45:51.952519Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/14.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz_")}
2025-06-04T13:45:51.952527Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T13:45:51.952593Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T13:45:52.053519Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/0.pack.gz_")}
2025-06-04T13:45:52.053542Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T13:45:52.053615Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T13:45:52.151988Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/17.pack.gz_")}
2025-06-04T13:45:52.151997Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T13:45:52.152056Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T13:45:52.253351Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/5b5924403aa3f821-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/finance/reports"), AnchoredSystemPathBuf("apps/frontend/.next/static/css/app"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/_app-pages-browser_node_modules_pnpm_tanstack_query-devtools_5_76_0_node_modules_tanstack_que-3f626e.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/8cb74166f4c238e7-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/9003d31b0bf0a63e-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/3612c969537d1f89-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/9efac92d680e57b6-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/b45a9d9d1da72c1b-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/b626faedfa289fd1-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/d03708b9b9cff7ea-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/d3c939daec3cf0fb-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/c3d28d0a4af5320e-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/555550940b3f3995-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/css/app/layout.css"), AnchoredSystemPathBuf("apps/frontend/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/628cf4a163773be3-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/finance/reports/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/main-app.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/078954fc9e8513ac-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/3abc2a74bccacda0-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/54685c0c990b5328-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/b53732993cf3ac57-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/ceced752341815e9-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/633457081244afec._.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/finance"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/e59a6a9b6eba13d5-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/layout.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/webpack.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/3ad6e6f895797568-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/cc4bdf7bf08e5351-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/029616a09f89e9be-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/683edc0915df848c-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/dbdc77a4a9d637b5-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app-pages-internals.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/finance/reports/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/18d6d756b83deaa1-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/0612184e5fd566f7-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/_app-pages-browser_node_modules_pnpm_tanstack_query-devtools_5_76_0_node_modules_tanstack_que-fef58b.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/23acdaac58de6ef3-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/css"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/24803c66df11f210-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/443b65745b6df830-s.woff2")}
2025-06-04T13:45:52.253397Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T13:45:52.264915Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T13:45:52.352566Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/14.pack.gz_")}
2025-06-04T13:45:52.352574Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T13:45:52.352616Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T13:45:52.852425Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/finance/reports/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/webpack.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/app"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.dd8bb029e583625c.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/layout.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/dd8bb029e583625c.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/app/layout.dd8bb029e583625c.hot-update.js")}
2025-06-04T13:45:52.852478Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T13:45:52.852600Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T13:45:52.952745Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/15.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/17.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/14.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/0.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/16.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz.old"), AnchoredSystemPathBuf("apps/frontend/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/finance/reports/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/0.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/15.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/7.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/16.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/14.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/17.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/7.pack.gz_")}
2025-06-04T13:45:52.952758Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T13:45:52.952823Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T13:45:54.053536Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/0.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/8.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/10.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/11.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/16.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/7.pack.gz_")}
2025-06-04T13:45:54.053551Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T13:45:54.053991Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T13:45:54.352619Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/7.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/8.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/16.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/0.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/11.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/4.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/10.pack.gz_")}
2025-06-04T13:45:54.352632Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T13:45:54.364936Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T13:45:54.451887Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/11.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/4.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/16.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/8.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/10.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/0.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/4.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/7.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/10.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/8.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/7.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz.old"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/0.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/11.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/16.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz")}
2025-06-04T13:45:54.451895Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T13:45:54.451947Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T13:46:01.753173Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/finance/budget/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/finance/budget"), AnchoredSystemPathBuf("apps/frontend/.next/server/webpack-runtime.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/finance/budget/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/layout.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-progress@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/cache-life.d.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/finance/reports/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/finance/reports/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/lucide-react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/finance/budget"), AnchoredSystemPathBuf("apps/frontend/.next/types/package.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.js")}
2025-06-04T13:46:01.753190Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T13:46:01.753283Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T13:46:01.952998Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/finance/budget"), AnchoredSystemPathBuf("apps/frontend/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/webpack.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/finance/budget/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/a2e52470d01e88e6.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.a2e52470d01e88e6.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/finance/reports/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/finance/budget/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js")}
2025-06-04T13:46:01.953007Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T13:46:01.953052Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T13:46:02.552601Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/webpack-runtime.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/finance/budget/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/finance/reports/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/cache-life.d.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/finance/reports/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/package.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/finance/budget/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/layout.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.json")}
2025-06-04T13:46:02.552615Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T13:46:02.552697Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T13:46:02.752710Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.3436d07eec841076.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/3436d07eec841076.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/finance/reports/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/webpack.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/finance/budget/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js")}
2025-06-04T13:46:02.752732Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T13:46:02.752815Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T13:46:03.253191Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/types/app/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/finance/reports/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/package.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/finance/budget/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/layout.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/webpack-runtime.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/finance/reports/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/cache-life.d.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/finance/budget/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/lucide-react@<EMAIL>")}
2025-06-04T13:46:03.253278Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T13:46:03.253353Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T13:46:03.351894Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/f1e42840e77a5afc.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/finance/reports/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/webpack.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.f1e42840e77a5afc.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/page.js")}
2025-06-04T13:46:03.351908Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T13:46:03.372938Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T13:46:03.453275Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/webpack.js"), AnchoredSystemPathBuf("apps/frontend/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/finance/budget/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/f1e42840e77a5afc.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/finance/reports/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.f1e42840e77a5afc.hot-update.js")}
2025-06-04T13:46:03.453285Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T13:46:03.453343Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T13:46:17.652366Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/lucide-react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/finance/budget/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/finance/budget/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/cache-life.d.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/analytics/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/analytics/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/webpack-runtime.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/layout.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/package.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/finance/reports/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/analytics"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/finance/reports/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/analytics"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/page.ts")}
2025-06-04T13:46:17.652538Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T13:46:17.652833Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T13:46:17.952982Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/analytics"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/0ae5484c9db8cb07.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/analytics/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.0ae5484c9db8cb07.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/webpack.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/analytics/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/finance/reports/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/finance/budget/page_client-reference-manifest.js")}
2025-06-04T13:46:17.952995Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T13:46:17.953100Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T13:47:17.853264Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/15.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/4.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/14.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/19.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/18.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/16.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/17.pack.gz_")}
2025-06-04T13:47:17.853292Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T13:47:17.854797Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T13:47:17.952705Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/15.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz.old")}
2025-06-04T13:47:17.952714Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T13:47:17.952762Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T13:47:18.052765Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/18.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/19.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/16.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/15.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/17.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/18.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/4.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/17.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/15.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/7.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/14.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/19.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/16.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/10.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/5.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/17.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/4.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/4.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/14.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/18.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz")}
2025-06-04T13:47:18.052781Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T13:47:18.064587Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T13:47:18.152428Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/7.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/10.pack.gz_")}
2025-06-04T13:47:18.152436Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T13:47:18.152487Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T13:47:18.252316Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/18.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/4.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/5.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/17.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/10.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/7.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/10.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/18.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/5.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz.old"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/7.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/17.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/4.pack.gz_")}
2025-06-04T13:47:18.252324Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T13:47:18.252599Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T14:06:08.304012Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("missing.md")}
2025-06-04T14:06:08.305617Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-04T14:06:11.084124Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("missing.md")}
2025-06-04T14:06:11.084161Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-04T14:06:33.484940Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("missing.md")}
2025-06-04T14:06:33.484974Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-04T14:06:46.586096Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/finance/reports/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/finance/budget/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/team/sales"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/layout.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/team/sales/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/finance/budget/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/cache-life.d.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/webpack-runtime.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/team/sales/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/team"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/team/sales"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/analytics/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/team"), AnchoredSystemPathBuf("apps/frontend/.next/types/package.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/lucide-react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/analytics/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/finance/reports/page.js")}
2025-06-04T14:06:46.589462Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T14:06:46.590093Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T14:06:46.984657Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/team"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/finance/budget/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/team/sales"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.6896abbe2e5ad56a.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/team/sales/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/finance/reports/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/analytics/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/webpack.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/6896abbe2e5ad56a.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/team/sales/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/build-manifest.json")}
2025-06-04T14:06:46.984668Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T14:06:46.984723Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
